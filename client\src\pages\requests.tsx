import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  AlertTriangle,
  Calendar,
  Check,
  CheckCircle,
  CreditCard,
  DollarSign,
  Eye,
  FileText,
  Filter,
  Play,
  Search,
  Star,
  TrendingUp,
  User,
  X,
} from "lucide-react"
import { useState } from "react"

// Icon mapping for dynamic icon rendering
const iconMap = {
  Play,
  Check,
  AlertTriangle,
};

const getColorClasses = (color: string, type: "bg" | "text") => {
  const colors = {
    blue: { bg: "bg-blue-100", text: "text-blue-600" },
    amber: { bg: "bg-amber-100", text: "text-amber-600" },
    green: { bg: "bg-green-100", text: "text-green-600" },
    red: { bg: "bg-red-100", text: "text-red-600" },
  }
  return colors[color as keyof typeof colors]?.[type] || colors.blue[type]
}

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case "high":
      return "bg-red-100 text-red-800"
    case "medium":
      return "bg-amber-100 text-amber-800"
    case "low":
      return "bg-green-100 text-green-800"
    case "urgent":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "approved":
      return "bg-green-100 text-green-800"
    case "under review":
      return "bg-amber-100 text-amber-800"
    case "rejected":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getRecommendationColor = (recommendation: string) => {
  switch (recommendation.toLowerCase()) {
    case "approve":
      return "bg-green-100 text-green-800"
    case "review":
      return "bg-amber-100 text-amber-800"
    case "reject":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export default function Requests() {
  const [selectedRequest, setSelectedRequest] = useState<any>(null)
  const [isDecisionReportOpen, setIsDecisionReportOpen] = useState(false)
  const queryClient = useQueryClient()

  // Mutation for updating request status
  const updateRequestMutation = useMutation({
    mutationFn: async ({ requestId, status }: { requestId: string, status: string }) => {
      // Extract numeric ID from formatted ID (e.g., "#REQ-123" -> 123)
      const numericId = parseInt(requestId.replace('#REQ-', ''))

      const response = await fetch(`/api/requests/${numericId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          status,
          reviewedBy: 'Admin1' // You might want to get this from user context
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update request')
      }

      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch the requests data
      queryClient.invalidateQueries({ queryKey: ["/api/requests"] })
      queryClient.invalidateQueries({ queryKey: ["/api/requests/stats"] })
    },
  })

  const { data: requestStats, isLoading: statsLoading } = useQuery({
    queryKey: ["/api/requests/stats"],
  });

  const { data: requests, isLoading: requestsLoading } = useQuery({
    queryKey: ["/api/requests"],
  });

  const openDecisionReport = (request: any) => {
    setSelectedRequest(request)
    setIsDecisionReportOpen(true)
  }

  if (statsLoading || requestsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading requests...</p>
        </div>
      </div>
    );
  }

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case "low":
        return "text-green-600 bg-green-100"
      case "medium":
        return "text-amber-600 bg-amber-100"
      case "high":
        return "text-red-600 bg-red-100"
      default:
        return "text-gray-600 bg-gray-100"
    }
  }

  return (
      <div>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Finance Requests Management</h1>
              <p className="text-gray-600">Track and manage all finance applications</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  <FileText className="w-4 h-4 mr-2" />
                  Evaluation Criteria
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Star className="w-5 h-5" />
                    Request Evaluation Criteria
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-6 py-4">
                  <div className="space-y-4">
                    <div className="border-l-4 border-red-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Repayment History
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • More than 2 missed payments in the past 12 months →{" "}
                          <span className="font-semibold text-red-600">REJECTED</span>
                        </p>
                        <p>
                          • Loan arrears exceed 30 days → <span className="font-semibold text-red-600">REJECTED</span>
                        </p>
                        <p>
                          • Repayment to financed ratio ≥ 90% →{" "}
                          <span className="font-semibold text-green-600">Positive, can increase credit limit</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-blue-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <CreditCard className="w-4 h-4" />
                        POS Data / Volume
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • Average monthly POS volume {"<"} $2,000 →{" "}
                          <span className="font-semibold text-red-600">REJECTED</span>
                        </p>
                        <p>
                          • POS volume {">"} $2,000 →{" "}
                          <span className="font-semibold text-green-600">Positive indicator</span>
                        </p>
                        <p>
                          • POS-based credit limit: <span className="font-semibold">3x average monthly POS</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Average Account Balance
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • If average account balance {"<"} $1,000 →{" "}
                          <span className="font-semibold text-red-600">REJECTED</span>
                        </p>
                        <p>
                          • Credit limit based on <span className="font-semibold">1.5x average balance</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-amber-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <TrendingUp className="w-4 h-4" />
                        Average Inflow / Outflow Ratio
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • Ratio {"<"} 0.85 →{" "}
                          <span className="font-semibold text-red-600">REJECTED due to unstable cash flow</span>
                        </p>
                        <p>
                          • Ratio ≥ 0.85 → <span className="font-semibold text-green-600">Acceptable</span>
                        </p>
                        <p>
                          • Credit limit based on <span className="font-semibold">2x average inflow</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-purple-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4" />
                        Combined Weak Indicators
                      </h3>
                      <div className="text-gray-600 text-sm mt-1">
                        <p>
                          If POS volume {"<"} $3,000 <span className="font-semibold">AND</span> inflow/outflow ratio {"<"}{" "}
                          0.9 <span className="font-semibold">AND</span> missed payments ≥ 1 →{" "}
                          <span className="font-semibold text-red-600">REJECTED</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-indigo-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <CheckCircle className="w-4 h-4" />
                        Credit Limit Calculation
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • Base credit limit ={" "}
                          <span className="font-semibold">minimum of (3x POS, 2x inflow, 1.5x balance)</span>
                        </p>
                        <p>
                          • If repayment ratio ≥ 90%, credit limit is{" "}
                          <span className="font-semibold text-green-600">increased by 25%</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-gray-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        Final Decision
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • If all rules pass → <span className="font-semibold text-green-600">APPROVED</span>
                        </p>
                        <p>
                          • If one or more critical thresholds not met →{" "}
                          <span className="font-semibold text-red-600">REJECTED with specific reasons</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Request Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          {requestStats.map((stat) => {
            const Icon = stat.icon
            return (
                <Card key={stat.title} className="border border-gray-200">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div
                          className={`w-12 h-12 ${getColorClasses(stat.color, "bg")} rounded-lg flex items-center justify-center`}
                      >
                        <Icon className={`${getColorClasses(stat.color, "text")} w-6 h-6`} />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
            )
          })}
        </div>

        {/* Filters and Search */}
        <Card className="border border-gray-200 mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                    placeholder="Search loan applications..."
                    className="pl-10 border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <Select>
                <SelectTrigger className="w-full sm:w-48 border-gray-300">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select>
                <SelectTrigger className="w-full sm:w-48 border-gray-300">
                  <SelectValue placeholder="Sector" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="restaurant">Restaurant</SelectItem>
                  <SelectItem value="entertainment">Entertainment</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="border-gray-300 bg-transparent">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Requests Table */}
        <Card className="border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Applicant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sector
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  System Recommendation
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reviewed By
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reviewed At
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
              {requests?.map((request: any) => (
                  <tr key={request.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{request.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{request.clientName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{request.clientSector}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">{request.amount}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm  text-gray-900">{request.submissionDate}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                      <Badge
                          variant="secondary"
                          className={getRecommendationColor(request.decisionReport.recommendation)}
                      >
                        {request.decisionReport.recommendation}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="secondary" className={getStatusColor(request.status)}>
                        {request.status}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{request.reviewedBy}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{request.reviewedAt}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-3">
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-primary hover:text-primary/80"
                            onClick={() => openDecisionReport(request)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {/*
                      <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-900">
                        <CheckCircle className="w-4 h-4" />
                      </Button>
                      */}
                      </div>
                    </td>
                  </tr>
              ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="bg-white px-6 py-3 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {requests.length} results
            </div>
            {/* <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="default" size="sm">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                3
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div> */}
          </div>
        </Card>

        {/* Decision Report Dialog */}
        <Dialog open={isDecisionReportOpen} onOpenChange={setIsDecisionReportOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Loan Decision Report - {selectedRequest?.id}
              </DialogTitle>
            </DialogHeader>
            {selectedRequest && (
                <div className="space-y-6 py-4">
                  {/* Applicant Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <User className="w-4 h-4" />
                      Applicant Information
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Name:</span>
                        <span className="ml-2 font-medium">{selectedRequest.clientName}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Sector:</span>
                        <span className="ml-2 font-medium">{selectedRequest.clientSector}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Years in Business:</span>
                        <span className="ml-2 font-medium">{selectedRequest.years} years</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Amount Requested:</span>
                        <span className="ml-2 font-medium text-green-600">{selectedRequest.amount}</span>
                      </div>
                    </div>
                  </div>

                  {/* Financial Data */}
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <DollarSign className="w-4 h-4" />
                      Financial Data
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-600">Account Balance:</span>
                          <span className="ml-2 font-bold text-blue-600">
                        ${selectedRequest.account_balance.toLocaleString()}
                      </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Monthly Inflow:</span>
                          <span className="ml-2 font-medium text-green-600">
                        ${selectedRequest.inflow.toLocaleString()}
                      </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-600">Monthly Outflow:</span>
                          <span className="ml-2 font-medium text-red-600">${selectedRequest.outflow.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Inflow/Outflow Ratio:</span>
                          <span className="ml-2 font-medium">
                        {(selectedRequest.inflow / selectedRequest.outflow).toFixed(2)}
                      </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* POS Analytics */}
                  {selectedRequest.decisionReport.posAnalytics && (
                    <div className="border-l-4 border-orange-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                        <TrendingUp className="w-4 h-4" />
                        Point of Sales Analytics
                      </h3>

                      {/* Volume & Transaction Metrics */}
                      <div className="grid grid-cols-2 gap-6 mb-4">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-medium text-blue-900 mb-2">Volume Performance</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total Volume:</span>
                              <span className="font-medium">${selectedRequest.decisionReport.posAnalytics.totalVolume.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Average Daily:</span>
                              <span className="font-medium">${selectedRequest.decisionReport.posAnalytics.averageVolume.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Growth Rate:</span>
                              <span className={`font-medium ${selectedRequest.decisionReport.posAnalytics.volumeGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {selectedRequest.decisionReport.posAnalytics.volumeGrowthRate.toFixed(1)}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Trend:</span>
                              <span className={`font-medium capitalize ${
                                selectedRequest.decisionReport.posAnalytics.volumeTrend === 'increasing' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.volumeTrend === 'decreasing' ? 'text-red-600' : 'text-yellow-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.volumeTrend}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-medium text-green-900 mb-2">Transaction Metrics</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total Transactions:</span>
                              <span className="font-medium">{selectedRequest.decisionReport.posAnalytics.totalTransactions.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Daily Average:</span>
                              <span className="font-medium">{selectedRequest.decisionReport.posAnalytics.averageTransactionsPerDay.toFixed(0)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Avg Transaction:</span>
                              <span className="font-medium">${selectedRequest.decisionReport.posAnalytics.averageTransactionValue.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Trend:</span>
                              <span className={`font-medium capitalize ${
                                selectedRequest.decisionReport.posAnalytics.transactionTrend === 'increasing' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.transactionTrend === 'decreasing' ? 'text-red-600' : 'text-yellow-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.transactionTrend}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Risk Assessment */}
                      <div className="grid grid-cols-2 gap-6 mb-4">
                        <div className="bg-red-50 p-4 rounded-lg">
                          <h4 className="font-medium text-red-900 mb-2">Risk Indicators</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Chargeback Rate:</span>
                              <span className={`font-medium ${
                                selectedRequest.decisionReport.posAnalytics.chargebackRiskLevel === 'low' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.chargebackRiskLevel === 'medium' ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.averageChargebackRate.toFixed(2)}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Risk Level:</span>
                              <span className={`font-medium capitalize ${
                                selectedRequest.decisionReport.posAnalytics.chargebackRiskLevel === 'low' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.chargebackRiskLevel === 'medium' ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.chargebackRiskLevel}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Overall Risk Score:</span>
                              <span className={`font-medium ${
                                selectedRequest.decisionReport.posAnalytics.riskScore < 30 ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.riskScore < 60 ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.riskScore.toFixed(0)}/100
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Chargeback Trend:</span>
                              <span className={`font-medium capitalize ${
                                selectedRequest.decisionReport.posAnalytics.chargebackTrend === 'improving' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.chargebackTrend === 'worsening' ? 'text-red-600' : 'text-yellow-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.chargebackTrend}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-medium text-purple-900 mb-2">Business Insights</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Business Stability:</span>
                              <span className={`font-medium capitalize ${
                                selectedRequest.decisionReport.posAnalytics.businessStability === 'growing' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.businessStability === 'declining' ? 'text-red-600' : 'text-yellow-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.businessStability}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Seasonality:</span>
                              <span className="font-medium capitalize">
                                {selectedRequest.decisionReport.posAnalytics.seasonalityIndicator}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Active Terminals:</span>
                              <span className="font-medium">{selectedRequest.decisionReport.posAnalytics.activeTerminals}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Recent Trend:</span>
                              <span className={`font-medium capitalize ${
                                selectedRequest.decisionReport.posAnalytics.recentPerformance.trend === 'positive' ? 'text-green-600' :
                                selectedRequest.decisionReport.posAnalytics.recentPerformance.trend === 'negative' ? 'text-red-600' : 'text-yellow-600'
                              }`}>
                                {selectedRequest.decisionReport.posAnalytics.recentPerformance.trend}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Terminal Performance */}
                      {selectedRequest.decisionReport.posAnalytics.terminalPerformance.length > 0 && (
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-3">Terminal Performance</h4>
                          <div className="space-y-2">
                            {selectedRequest.decisionReport.posAnalytics.terminalPerformance.map((terminal, index) => (
                              <div key={index} className="flex justify-between items-center p-2 bg-white rounded border">
                                <div className="flex items-center gap-3">
                                  <span className="font-medium text-sm">{terminal.terminalId}</span>
                                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                                    terminal.performance === 'excellent' ? 'bg-green-100 text-green-800' :
                                    terminal.performance === 'good' ? 'bg-blue-100 text-blue-800' :
                                    terminal.performance === 'average' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {terminal.performance}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  ${terminal.volume.toLocaleString()} | {terminal.transactions} txns | {terminal.chargebackRate.toFixed(2)}% CB
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* System Recommendation */}
                  <div className="border-l-4 border-green-500 pl-4">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <TrendingUp className="w-4 h-4" />
                      System Recommendation
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-gray-600">Recommendation:</span>
                        <div className="mt-1 p-3 bg-green-50 border border-green-200 rounded-md">
                          <Badge className={getRecommendationColor(selectedRequest.decisionReport.recommendation)}>
                            {selectedRequest.decisionReport.recommendation}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600">Analysis Notes:</span>
                        <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md">
                          <p className="text-gray-700">{selectedRequest.decisionReport.notes}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="border-l-4 border-purple-500 pl-4">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <Calendar className="w-4 h-4" />
                      Application Timeline
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Submission Date:</span>
                        <span className="ml-2 font-medium">{selectedRequest.submissionDate}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Current Status:</span>
                        <Badge variant="secondary" className={getStatusColor(selectedRequest.status)}>
                          {selectedRequest.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Admin Decision Buttons */}
                  {selectedRequest.status.toLowerCase() === "under review" && (
                      <div className="border-t pt-6">
                        <h3 className="font-semibold text-gray-900 mb-4">Admin Decision</h3>
                        <div className="flex gap-4 justify-end">
                          <Button
                              variant="outline"
                              className="border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700 bg-transparent"
                              disabled={updateRequestMutation.isPending}
                              onClick={() => {
                                updateRequestMutation.mutate(
                                  { requestId: selectedRequest.id, status: "Rejected" },
                                  {
                                    onSuccess: () => {
                                      setIsDecisionReportOpen(false)
                                    },
                                    onError: (error) => {
                                      console.error("Failed to reject request:", error)
                                      // You might want to show a toast notification here
                                    }
                                  }
                                )
                              }}
                          >
                            <X className="w-4 h-4 mr-2" />
                            {updateRequestMutation.isPending ? "Rejecting..." : "Reject Request"}
                          </Button>
                          <Button
                              className="bg-green-600 hover:bg-green-700 text-white"
                              disabled={updateRequestMutation.isPending}
                              onClick={() => {
                                updateRequestMutation.mutate(
                                  { requestId: selectedRequest.id, status: "Approved" },
                                  {
                                    onSuccess: () => {
                                      setIsDecisionReportOpen(false)
                                    },
                                    onError: (error) => {
                                      console.error("Failed to approve request:", error)
                                      // You might want to show a toast notification here
                                    }
                                  }
                                )
                              }}
                          >
                            <CheckCircle className="w-4 h-4 mr-2" />
                            {updateRequestMutation.isPending ? "Approving..." : "Approve Request"}
                          </Button>
                        </div>
                      </div>
                  )}
                </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
  )
}
