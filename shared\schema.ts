import { date, decimal, integer, pgTable, serial, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const clients = pgTable("clients", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  sector: text("sector"),
  years_in_business: integer("years_in_business"),
  status: text("status"),
  registration_date: date("registration_date"),
  collateral_loan_ratio: decimal("collateral_loan_ratio", { precision: 5, scale: 4 }),
  months_vat: integer("months_vat"),
  current_balance: decimal("current_balance", { precision: 15, scale: 2 }),
  avg_balance_6m: decimal("avg_balance_6m", { precision: 15, scale: 2 }),
  min_balance_3m: decimal("min_balance_3m", { precision: 15, scale: 2 }),
  inflow_outflow_ratio: decimal("inflow_outflow_ratio", { precision: 8, scale: 4 }),
  inflow: decimal("inflow", { precision: 15, scale: 2 }),
  outflow: decimal("outflow", { precision: 15, scale: 2 }),
  avg_pos: decimal("avg_pos", { precision: 15, scale: 2 }),
  pos_growth_3m: decimal("pos_growth_3m", { precision: 8, scale: 4 }),
  chargeback_rate: decimal("chargeback_rate", { precision: 5, scale: 4 }),
  repayment_missed_12m: integer("repayment_missed_12m"),
  finance_used_12m: decimal("finance_used_12m", { precision: 15, scale: 2 }),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export type Client = typeof clients.$inferSelect;
export type InsertClient = typeof clients.$inferInsert;

// Point of Sales table
export const pointOfSales = pgTable("point_of_sales", {
  id: serial("id").primaryKey(),
  client_id: integer("client_id").notNull().references(() => clients.id, { onDelete: "cascade" }),
  date: date("date").notNull(),
  transaction_count: integer("transaction_count"),
  volume: decimal("volume", { precision: 15, scale: 2 }),
  avg_transaction: decimal("avg_transaction", { precision: 10, scale: 2 }),
  chargeback: decimal("chargeback", { precision: 10, scale: 2 }),
  created_at: timestamp("created_at").defaultNow(),
});

export type PointOfSale = typeof pointOfSales.$inferSelect;
export type InsertPointOfSale = typeof pointOfSales.$inferInsert;

// Transactions table
export const transactions = pgTable("transactions", {
  id: serial("id").primaryKey(),
  client_id: integer("client_id").notNull().references(() => clients.id, { onDelete: "cascade" }),
  date: date("date").notNull(),
  type: text("type"),
  amount: decimal("amount", { precision: 15, scale: 2 }),
  description: text("description"),
  created_at: timestamp("created_at").defaultNow(),
});

export type Transaction = typeof transactions.$inferSelect;
export type InsertTransaction = typeof transactions.$inferInsert;

// Loans table
export const loans = pgTable("loans", {
  id: serial("id").primaryKey(),
  client_id: integer("client_id").notNull().references(() => clients.id, { onDelete: "cascade" }),
  date: date("date").notNull(),
  amount: decimal("amount", { precision: 15, scale: 2 }),
  status: text("status"),
  purpose: text("purpose"),
  current_utilization: decimal("current_utilization", { precision: 15, scale: 2 }),
  repaid_ratio: decimal("repaid_ratio", { precision: 5, scale: 4 }),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export type Loan = typeof loans.$inferSelect;
export type InsertLoan = typeof loans.$inferInsert;

// Requests table
export const requests = pgTable("requests", {
  id: serial("id").primaryKey(),
  client_id: integer("client_id").notNull().references(() => clients.id, { onDelete: "cascade" }),
  date: date("date").notNull(),
  amount: decimal("amount", { precision: 15, scale: 2 }),
  purpose: text("purpose"),
  system_recommendation: text("system_recommendation"),
  analysis_report: text("analysis_report"),
  status: text("status"),
  reviewed_by: text("reviewed_by"),
  reviewed_at: date("reviewed_at"),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export type Request = typeof requests.$inferSelect;
export type InsertRequest = typeof requests.$inferInsert;


