import { clients } from "@shared/schema";
import { db } from "./storage.js";

/**
 * Initialize the database by creating tables and running migrations
 */
export async function initializeDatabase() {
  try {
    console.log("Initializing database...");
    
    // Test the connection
    await db.select().from(clients).limit(1);
    console.log("✅ Database connection successful");
    
    return true;
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    return false;
  }
}
