import type { PointOfSale } from "@shared/schema";

export interface POSAnalytics {
  // Volume and Transaction Metrics
  totalVolume: number;
  averageVolume: number;
  volumeTrend: 'increasing' | 'decreasing' | 'stable';
  volumeGrowthRate: number;
  
  // Transaction Patterns
  totalTransactions: number;
  averageTransactionsPerDay: number;
  averageTransactionValue: number;
  transactionTrend: 'increasing' | 'decreasing' | 'stable';
  
  // Risk Indicators
  averageChargebackRate: number;
  chargebackTrend: 'improving' | 'worsening' | 'stable';
  chargebackRiskLevel: 'low' | 'medium' | 'high';
  
  // Terminal Performance
  activeTerminals: number;
  terminalPerformance: Array<{
    terminalId: string;
    volume: number;
    transactions: number;
    chargebackRate: number;
    performance: 'excellent' | 'good' | 'average' | 'poor';
  }>;
  
  // Business Insights
  businessStability: 'stable' | 'growing' | 'declining';
  seasonalityIndicator: 'seasonal' | 'consistent';
  riskScore: number; // 0-100, lower is better
  
  // Recent Performance (last 7 days)
  recentPerformance: {
    volume: number;
    transactions: number;
    chargebackRate: number;
    trend: 'positive' | 'negative' | 'neutral';
  };
}

export function calculatePOSAnalytics(posData: PointOfSale[]): POSAnalytics {
  if (!posData || posData.length === 0) {
    return getEmptyAnalytics();
  }

  // Sort data by date (newest first)
  const sortedData = [...posData].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  // Calculate volume metrics
  const totalVolume = sortedData.reduce((sum, pos) => sum + parseFloat(pos.volume?.toString() || '0'), 0);
  const averageVolume = totalVolume / sortedData.length;
  
  // Calculate transaction metrics
  const totalTransactions = sortedData.reduce((sum, pos) => sum + (pos.transaction_count || 0), 0);
  const averageTransactionsPerDay = totalTransactions / sortedData.length;
  const averageTransactionValue = totalTransactions > 0 ? totalVolume / totalTransactions : 0;
  
  // Calculate chargeback metrics
  const averageChargebackRate = sortedData.reduce((sum, pos) => 
    sum + parseFloat(pos.chargeback?.toString() || '0'), 0) / sortedData.length;
  
  // Calculate trends (comparing first half vs second half of data)
  const midPoint = Math.floor(sortedData.length / 2);
  const recentData = sortedData.slice(0, midPoint);
  const olderData = sortedData.slice(midPoint);
  
  const recentAvgVolume = recentData.reduce((sum, pos) => 
    sum + parseFloat(pos.volume?.toString() || '0'), 0) / recentData.length;
  const olderAvgVolume = olderData.reduce((sum, pos) => 
    sum + parseFloat(pos.volume?.toString() || '0'), 0) / olderData.length;
  
  const volumeGrowthRate = olderAvgVolume > 0 ? ((recentAvgVolume - olderAvgVolume) / olderAvgVolume) * 100 : 0;
  const volumeTrend = getVolumetrend(volumeGrowthRate);
  
  // Calculate transaction trend
  const recentAvgTransactions = recentData.reduce((sum, pos) => 
    sum + (pos.transaction_count || 0), 0) / recentData.length;
  const olderAvgTransactions = olderData.reduce((sum, pos) => 
    sum + (pos.transaction_count || 0), 0) / olderData.length;
  
  const transactionGrowthRate = olderAvgTransactions > 0 ? 
    ((recentAvgTransactions - olderAvgTransactions) / olderAvgTransactions) * 100 : 0;
  const transactionTrend = getVolumetrend(transactionGrowthRate);
  
  // Calculate chargeback trend
  const recentAvgChargeback = recentData.reduce((sum, pos) => 
    sum + parseFloat(pos.chargeback?.toString() || '0'), 0) / recentData.length;
  const olderAvgChargeback = olderData.reduce((sum, pos) => 
    sum + parseFloat(pos.chargeback?.toString() || '0'), 0) / olderData.length;
  
  const chargebackTrend = getChargebackTrend(recentAvgChargeback, olderAvgChargeback);
  const chargebackRiskLevel = getChargebackRiskLevel(averageChargebackRate);
  
  // Calculate terminal performance
  const terminalMap = new Map<string, { volume: number; transactions: number; chargeback: number; count: number }>();
  
  sortedData.forEach(pos => {
    const terminalId = `POS-${pos.client_id}`;
    const existing = terminalMap.get(terminalId) || { volume: 0, transactions: 0, chargeback: 0, count: 0 };
    
    existing.volume += parseFloat(pos.volume?.toString() || '0');
    existing.transactions += pos.transaction_count || 0;
    existing.chargeback += parseFloat(pos.chargeback?.toString() || '0');
    existing.count += 1;
    
    terminalMap.set(terminalId, existing);
  });
  
  const terminalPerformance = Array.from(terminalMap.entries()).map(([terminalId, data]) => ({
    terminalId,
    volume: data.volume,
    transactions: data.transactions,
    chargebackRate: data.chargeback / data.count,
    performance: getTerminalPerformance(data.volume, data.transactions, data.chargeback / data.count)
  }));
  
  // Calculate business insights
  const businessStability = getBusinessStability(volumeTrend, transactionTrend, chargebackTrend);
  const seasonalityIndicator = getSeasonalityIndicator(sortedData);
  const riskScore = calculateRiskScore(averageChargebackRate, volumeTrend, businessStability);
  
  // Recent performance (last 7 days or available data)
  const recentPerformanceData = sortedData.slice(0, Math.min(7, sortedData.length));
  const recentVolume = recentPerformanceData.reduce((sum, pos) => 
    sum + parseFloat(pos.volume?.toString() || '0'), 0);
  const recentTransactions = recentPerformanceData.reduce((sum, pos) => 
    sum + (pos.transaction_count || 0), 0);
  const recentChargebackRate = recentPerformanceData.reduce((sum, pos) => 
    sum + parseFloat(pos.chargeback?.toString() || '0'), 0) / recentPerformanceData.length;
  
  const recentTrend = getRecentTrend(volumeGrowthRate, transactionGrowthRate, chargebackTrend);
  
  return {
    totalVolume,
    averageVolume,
    volumeTrend,
    volumeGrowthRate,
    totalTransactions,
    averageTransactionsPerDay,
    averageTransactionValue,
    transactionTrend,
    averageChargebackRate,
    chargebackTrend,
    chargebackRiskLevel,
    activeTerminals: terminalMap.size,
    terminalPerformance,
    businessStability,
    seasonalityIndicator,
    riskScore,
    recentPerformance: {
      volume: recentVolume,
      transactions: recentTransactions,
      chargebackRate: recentChargebackRate,
      trend: recentTrend
    }
  };
}

// Helper functions
function getEmptyAnalytics(): POSAnalytics {
  return {
    totalVolume: 0,
    averageVolume: 0,
    volumeTrend: 'stable',
    volumeGrowthRate: 0,
    totalTransactions: 0,
    averageTransactionsPerDay: 0,
    averageTransactionValue: 0,
    transactionTrend: 'stable',
    averageChargebackRate: 0,
    chargebackTrend: 'stable',
    chargebackRiskLevel: 'low',
    activeTerminals: 0,
    terminalPerformance: [],
    businessStability: 'stable',
    seasonalityIndicator: 'consistent',
    riskScore: 0,
    recentPerformance: {
      volume: 0,
      transactions: 0,
      chargebackRate: 0,
      trend: 'neutral'
    }
  };
}

function getVolumetrend(growthRate: number): 'increasing' | 'decreasing' | 'stable' {
  if (growthRate > 5) return 'increasing';
  if (growthRate < -5) return 'decreasing';
  return 'stable';
}

function getChargebackTrend(recent: number, older: number): 'improving' | 'worsening' | 'stable' {
  const diff = recent - older;
  if (diff > 0.1) return 'worsening';
  if (diff < -0.1) return 'improving';
  return 'stable';
}

function getChargebackRiskLevel(rate: number): 'low' | 'medium' | 'high' {
  if (rate > 1.0) return 'high';
  if (rate > 0.5) return 'medium';
  return 'low';
}

function getTerminalPerformance(volume: number, transactions: number, chargebackRate: number): 'excellent' | 'good' | 'average' | 'poor' {
  const avgTransaction = transactions > 0 ? volume / transactions : 0;
  
  if (chargebackRate > 1.0 || avgTransaction < 20) return 'poor';
  if (chargebackRate > 0.5 || avgTransaction < 50) return 'average';
  if (chargebackRate < 0.2 && avgTransaction > 100) return 'excellent';
  return 'good';
}

function getBusinessStability(volumeTrend: string, transactionTrend: string, chargebackTrend: string): 'stable' | 'growing' | 'declining' {
  if (volumeTrend === 'increasing' && transactionTrend === 'increasing' && chargebackTrend !== 'worsening') {
    return 'growing';
  }
  if (volumeTrend === 'decreasing' || transactionTrend === 'decreasing' || chargebackTrend === 'worsening') {
    return 'declining';
  }
  return 'stable';
}

function getSeasonalityIndicator(data: PointOfSale[]): 'seasonal' | 'consistent' {
  // Simple seasonality check based on volume variance
  const volumes = data.map(pos => parseFloat(pos.volume?.toString() || '0'));
  const mean = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
  const variance = volumes.reduce((sum, vol) => sum + Math.pow(vol - mean, 2), 0) / volumes.length;
  const coefficient = Math.sqrt(variance) / mean;
  
  return coefficient > 0.3 ? 'seasonal' : 'consistent';
}

function calculateRiskScore(chargebackRate: number, volumeTrend: string, businessStability: string): number {
  let score = 0;
  
  // Chargeback risk (0-40 points)
  score += Math.min(chargebackRate * 40, 40);
  
  // Volume trend risk (0-30 points)
  if (volumeTrend === 'decreasing') score += 30;
  else if (volumeTrend === 'stable') score += 10;
  
  // Business stability risk (0-30 points)
  if (businessStability === 'declining') score += 30;
  else if (businessStability === 'stable') score += 10;
  
  return Math.min(score, 100);
}

function getRecentTrend(volumeGrowth: number, transactionGrowth: number, chargebackTrend: string): 'positive' | 'negative' | 'neutral' {
  if (volumeGrowth > 0 && transactionGrowth > 0 && chargebackTrend !== 'worsening') {
    return 'positive';
  }
  if (volumeGrowth < 0 || transactionGrowth < 0 || chargebackTrend === 'worsening') {
    return 'negative';
  }
  return 'neutral';
}
