import type { Express } from "express";
import { createServer, type Server } from "http";
import { quickActions, recentActivities } from "./data/dashboard";
import { storage } from "./storage";

export async function registerRoutes(app: Express): Promise<Server> {
  // Dashboard API endpoints
  app.get("/api/dashboard/stats", async (_req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });

  app.get("/api/dashboard/activities", (_req, res) => {
    res.json(recentActivities);
  });

  app.get("/api/dashboard/quick-actions", (_req, res) => {
    res.json(quickActions);
  });

  // Clients API endpoints
  app.get("/api/clients", async (_req, res) => {
    // res.json(mockClients);
    // fetching from database
    try {
      const clients = await storage.getClients();
      res.json(clients);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch clients" });
    }
  });

  app.get("/api/clients/:id", async (req, res) => {
    try {
      const clientId = parseInt(req.params.id);
      const client = await storage.getClient(clientId);

      if (!client) {
        return res.status(404).json({ error: "Client not found" });
      }

      // Get additional data for the client
      const transactions = await storage.getTransactions(clientId);
      const loans = await storage.getLoans(clientId);
      const posData = await storage.getPointOfSales(clientId);

      // Format the response to match the expected structure
      const clientDetails = {
        id: client.id,
        name: client.name,
        sector: client.sector,
        years: client.years_in_business,
        status: client.status,
        registrationDate: client.registration_date,
        email: "<EMAIL>", // Not in DB, using placeholder
        phone: "+965 1234 5678", // Not in DB, using placeholder
        address: "Kuwait City, Kuwait", // Not in DB, using placeholder
        description: "Business description", // Not in DB, using placeholder
        account_balance: parseFloat(client.current_balance || '0'),
        averageBalance6Months: parseFloat(client.avg_balance_6m || '0'),
        minimumBalance3Months: parseFloat(client.min_balance_3m || '0'),
        inflowOutflowRatio: parseFloat(client.inflow_outflow_ratio || '0'),
        inflow: parseFloat(client.inflow || '0'),
        outflow: parseFloat(client.outflow || '0'),
        avgPosVolume3Months: parseFloat(client.avg_pos || '0'),
        posGrowthRate3Months: parseFloat(client.pos_growth_3m || '0') * 100,
        chargebackRate: parseFloat(client.chargeback_rate || '0') * 100,
        repaymentMissed12Months: client.repayment_missed_12m || 0,
        financeUsed12Months: parseFloat(client.finance_used_12m || '0'),
        collateralToLoanRatio: parseFloat(client.collateral_loan_ratio || '0'),
        monthsSinceVATFiling: client.months_vat || 0,
        recentTransactions: transactions.slice(0, 5).map(t => ({
          id: t.id,
          date: t.date,
          type: t.type || 'Unknown',
          amount: parseFloat(t.amount || '0'),
          description: t.description || ''
        })),
        loanHistory: loans.map(l => ({
          id: l.id,
          amount: `KWD ${parseFloat(l.amount || '0').toLocaleString()}`,
          status: l.status || 'Unknown',
          date: l.date,
          purpose: l.purpose || '',
          currentUtilization: parseFloat(l.current_utilization || '0'),
          repaidRatio: parseFloat(l.repaid_ratio || '0') * 100
        })),
        posData: posData.slice(0, 5).map(p => ({
          id: p.id,
          date: p.date,
          terminalId: `POS-${client.id}`,
          transactionCount: p.transaction_count || 0,
          volume: parseFloat(p.volume || '0'),
          averageTransaction: parseFloat(p.avg_transaction || '0'),
          chargeback: parseFloat(p.chargeback || '0')
        }))
      };
      console.log("client deatils:")
      console.log(clientDetails);
      res.json(clientDetails);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch client details" });
    }
  });

  // Requests API endpoints
  app.get("/api/requests/stats", async (_req, res) => {
    try {
      const stats = await storage.getRequestStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch request stats" });
    }
  });

  app.get("/api/requests", async (_req, res) => {
    try {
      const requests = await storage.getRequests();

      // Format the requests to match the expected structure
      const formattedRequests = await Promise.all(requests.map(async (request) => {
        const client = await storage.getClient(request.client_id);

        return {
          id: `#REQ-${request.id}`,
          clientName: client?.name || 'Unknown Client',
          clientSector: client?.sector || 'Unknown',
          years: client?.years_in_business || 0,
          account_balance: parseFloat(client?.current_balance || '0'),
          inflow: parseFloat(client?.inflow || '0'),
          outflow: parseFloat(client?.outflow || '0'),
          amount: `KWD ${parseFloat(request.amount || '0').toLocaleString()}`,
          status: request.status || 'Unknown',
          submissionDate: request.date,
          decisionReport: {
            creditScore: 720, // This would need to be calculated or stored
            debtToIncomeRatio: "35%", // This would need to be calculated
            annualRevenue: `$${(parseFloat(client?.inflow || '0') * 12).toLocaleString()}`,
            yearsInBusiness: client?.years_in_business || 0,
            riskAssessment: "Medium", // This would need to be calculated
            recommendation: request.system_recommendation || "Review",
            notes: request.analysis_report || "No analysis available"
          },
          reviewedBy: request.reviewed_by || "",
          reviewedAt: request.reviewed_at || ""
        };
      }));

      res.json(formattedRequests);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch requests" });
    }
  });

  app.patch("/api/requests/:id", async (req, res) => {
    try {
      const requestId = parseInt(req.params.id);
      const { status, reviewedBy } = req.body;

      if (!status) {
        return res.status(400).json({ error: "Status is required" });
      }

      // Validate status values
      const validStatuses = ['Under Review', 'Approved', 'Rejected'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({ error: "Invalid status value" });
      }

      const updatedRequest = await storage.updateRequest(requestId, {
        status,
        reviewed_by: reviewedBy || 'Admin',
        reviewed_at: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
      });

      if (!updatedRequest) {
        return res.status(404).json({ error: "Request not found" });
      }

      res.json({ message: "Request updated successfully", request: updatedRequest });
    } catch (error) {
      console.error("Error updating request:", error);
      res.status(500).json({ error: "Failed to update request" });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
